#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用示例脚本
演示如何使用爬虫和数据库管理功能
"""

from weapp_ad_crawler import WeappAdCrawler
from database_manager import DatabaseManager
import json

def example_crawl():
    """示例：爬取数据"""
    print("=== 示例：爬取小程序广告数据 ===")
    
    # 创建爬虫实例
    crawler = WeappAdCrawler("example_ads.db")
    
    # 设置较高的阈值进行测试（只爬取少量数据）
    crawler.meet_count_threshold = 1000
    
    # 爬取数据
    result = crawler.run(
        last_appear_time_gte=1751299200000,  # 2025-07-01
        last_appear_time_lte=1753891199999,  # 2025-07-31
        ad_type=5
    )
    
    print(f"爬取结果: {result}")

def example_database_operations():
    """示例：数据库操作"""
    print("\n=== 示例：数据库操作 ===")
    
    # 创建数据库管理器
    db_manager = DatabaseManager("weapp_ads.db")
    
    # 获取统计信息
    stats = db_manager.get_statistics()
    if stats:
        print(f"数据库统计:")
        print(f"  总记录数: {stats['total_ads']}")
        print(f"  MeetCount范围: {stats['meet_count_stats']['min']} - {stats['meet_count_stats']['max']}")
        print(f"  平均MeetCount: {stats['meet_count_stats']['avg']}")
    
    # 导出高质量数据
    print("\n导出meetCount >= 500的数据...")
    success = db_manager.export_data("high_meet_count_ads.json", 500)
    if success:
        print("导出成功!")

def example_data_analysis():
    """示例：数据分析"""
    print("\n=== 示例：数据分析 ===")
    
    try:
        # 读取导出的数据
        with open("high_quality_ads.json", "r", encoding="utf-8") as f:
            ads_data = json.load(f)
        
        print(f"高质量广告数据分析 (meetCount >= 100):")
        print(f"  总数量: {len(ads_data)}")
        
        if ads_data:
            # 分析meetCount分布
            meet_counts = [ad['meet_count'] for ad in ads_data]
            print(f"  MeetCount范围: {min(meet_counts)} - {max(meet_counts)}")
            print(f"  平均MeetCount: {sum(meet_counts) / len(meet_counts):.2f}")
            
            # 分析应用分布
            app_names = {}
            for ad in ads_data:
                app_name = ad.get('app_name', '未知')
                app_names[app_name] = app_names.get(app_name, 0) + 1
            
            print(f"  应用数量: {len(app_names)}")
            print(f"  热门应用 (前5名):")
            sorted_apps = sorted(app_names.items(), key=lambda x: x[1], reverse=True)
            for i, (app_name, count) in enumerate(sorted_apps[:5]):
                print(f"    {i+1}. {app_name}: {count} 条广告")
    
    except FileNotFoundError:
        print("未找到导出的数据文件，请先运行爬虫或导出数据")

if __name__ == "__main__":
    # 运行示例
    # example_crawl()  # 取消注释来运行爬虫示例
    example_database_operations()
    example_data_analysis()
