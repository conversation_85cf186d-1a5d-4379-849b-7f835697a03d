#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证脚本
验证新字段是否正确保存
"""

import sqlite3
import json

def verify_new_fields():
    """验证新字段数据"""
    print("=== 验证新字段数据 ===")
    
    conn = sqlite3.connect("weapp_ads.db")
    cursor = conn.cursor()
    
    try:
        # 检查表结构
        cursor.execute("PRAGMA table_info(weapp_ads)")
        columns = cursor.fetchall()
        
        print("数据库表结构:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 检查新字段数据
        cursor.execute('''
            SELECT ad_id, title, image_url, meet_count, app_name 
            FROM weapp_ads 
            WHERE title IS NOT NULL AND title != '' 
            ORDER BY meet_count DESC 
            LIMIT 10
        ''')
        
        records = cursor.fetchall()
        
        print(f"\n包含title字段的记录数: {len(records)}")
        print("\n前10条记录:")
        for i, (ad_id, title, image_url, meet_count, app_name) in enumerate(records, 1):
            print(f"{i}. AD ID: {ad_id[:20]}...")
            print(f"   标题: {title}")
            print(f"   应用: {app_name}")
            print(f"   MeetCount: {meet_count}")
            print(f"   图片URL: {image_url[:50]}..." if image_url else "   图片URL: 无")
            print()
        
        # 统计新字段填充情况
        cursor.execute('SELECT COUNT(*) FROM weapp_ads WHERE title IS NOT NULL AND title != ""')
        title_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM weapp_ads WHERE image_url IS NOT NULL AND image_url != ""')
        image_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM weapp_ads')
        total_count = cursor.fetchone()[0]
        
        print(f"字段填充统计:")
        print(f"  总记录数: {total_count}")
        print(f"  有标题的记录: {title_count} ({title_count/total_count*100:.1f}%)")
        print(f"  有图片URL的记录: {image_count} ({image_count/total_count*100:.1f}%)")
        
    except Exception as e:
        print(f"验证失败: {e}")
    finally:
        conn.close()

def check_recent_updates():
    """检查最近的更新记录"""
    print("\n=== 检查最近更新记录 ===")
    
    conn = sqlite3.connect("weapp_ads.db")
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT ad_id, title, meet_count, updated_at, created_at
            FROM weapp_ads 
            WHERE updated_at > created_at
            ORDER BY updated_at DESC 
            LIMIT 5
        ''')
        
        records = cursor.fetchall()
        
        print(f"最近更新的记录数: {len(records)}")
        for i, (ad_id, title, meet_count, updated_at, created_at) in enumerate(records, 1):
            print(f"{i}. AD ID: {ad_id[:20]}...")
            print(f"   标题: {title}")
            print(f"   MeetCount: {meet_count}")
            print(f"   创建时间: {created_at}")
            print(f"   更新时间: {updated_at}")
            print()
            
    except Exception as e:
        print(f"检查更新记录失败: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    verify_new_fields()
    check_recent_updates()
