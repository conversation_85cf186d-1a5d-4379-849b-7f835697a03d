#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本
用于测试API响应和调试问题
"""

import requests
import json
from config import API_CONFIG

def test_api():
    """测试API响应"""
    
    # 构建请求参数
    params = {
        'lastAppearTimeGte': 1751299200000,
        'lastAppearTimeLte': 1753891199999,
        'adType': 5,
        'pageNo': 1,
        'pageSize': 50,
        'sort': 'meet_count desc'
    }
    
    print("=== API测试 ===")
    print(f"请求URL: {API_CONFIG['base_url']}")
    print(f"请求参数: {params}")
    print()
    
    try:
        response = requests.get(
            API_CONFIG['base_url'],
            params=params,
            headers=API_CONFIG['headers'],
            cookies=API_CONFIG['cookies'],
            verify=False,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print()
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("响应JSON:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                
                # 分析响应数据
                if isinstance(data, dict):
                    code = data.get('code')
                    msg = data.get('msg', '')
                    response_data = data.get('data', {})
                    
                    print(f"\n=== 响应分析 ===")
                    print(f"返回码: {code}")
                    print(f"消息: {msg}")
                    
                    if isinstance(response_data, dict):
                        records = response_data.get('records', [])
                        total = response_data.get('total', 0)
                        print(f"总记录数: {total}")
                        print(f"当前页记录数: {len(records)}")
                        
                        if records:
                            print(f"第一条记录的meetCount: {records[0].get('meetCount', 'N/A')}")
                            print(f"最后一条记录的meetCount: {records[-1].get('meetCount', 'N/A')}")
                
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                print(f"响应文本: {response.text[:1000]}...")
        else:
            print(f"请求失败，响应文本: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    test_api()
