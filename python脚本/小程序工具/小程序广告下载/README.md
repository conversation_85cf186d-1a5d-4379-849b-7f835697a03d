# 小程序广告爬虫

这是一个用于爬取小程序广告数据的Python脚本，支持自动翻页和数据库存储。

## 功能特点

- 自动爬取小程序广告数据
- 按meetCount降序排列，当meetCount <= 10时自动停止翻页
- 数据存储到SQLite数据库
- 支持数据导出和统计分析
- 完整的日志记录
- 命令行界面操作

## 文件说明

- `weapp_ad_crawler.py` - 主爬虫脚本
- `database_manager.py` - 数据库管理脚本
- `run_crawler.py` - 命令行运行脚本
- `config.py` - 配置文件
- `requirements.txt` - Python依赖包
- `README.md` - 使用说明

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 初始化数据库

```bash
python run_crawler.py init
```

### 2. 运行爬虫

#### 使用默认参数爬取
```bash
python run_crawler.py crawl
```

#### 指定时间范围爬取
```bash
python run_crawler.py crawl --start-date 2025-07-01 --end-date 2025-07-31
```

#### 指定广告类型和阈值
```bash
python run_crawler.py crawl --ad-type 5 --threshold 15
```

#### 使用自定义数据库
```bash
python run_crawler.py crawl --database my_ads.db
```

### 3. 查看统计信息

```bash
python run_crawler.py stats
```

### 4. 导出数据

#### 导出meetCount >= 10的数据
```bash
python run_crawler.py export
```

#### 指定阈值和输出文件
```bash
python run_crawler.py export --threshold 20 --output high_quality_ads.json
```

## 数据库结构

### weapp_ads 表
- `id` - 自增主键
- `ad_id` - 广告ID
- `app_name` - 应用名称
- `app_id` - 应用ID
- `ad_type` - 广告类型
- `meet_count` - 遇见次数
- `last_appear_time` - 最后出现时间
- `first_appear_time` - 首次出现时间
- `ad_content` - 广告内容(JSON)
- `ad_images` - 广告图片(JSON)
- `ad_videos` - 广告视频(JSON)
- `landing_page` - 落地页
- `advertiser_info` - 广告主信息(JSON)
- `raw_data` - 原始数据(JSON)
- `created_at` - 创建时间
- `updated_at` - 更新时间

### crawl_logs 表
- `id` - 自增主键
- `page_no` - 页码
- `total_records` - 记录总数
- `min_meet_count` - 最小meetCount
- `max_meet_count` - 最大meetCount
- `crawl_time` - 爬取时间
- `status` - 状态
- `error_message` - 错误信息

## 配置说明

可以在 `config.py` 中修改以下配置：

- `meet_count_threshold` - meetCount阈值，默认为10
- `page_size` - 每页记录数，默认为50
- `delay_between_requests` - 请求间隔，默认为1秒
- `request_timeout` - 请求超时时间，默认为30秒

## 注意事项

1. 请确保网络连接正常
2. 如果遇到认证问题，需要更新config.py中的Authorization token
3. 爬虫会自动处理重复数据，使用ad_id作为唯一标识
4. 建议定期备份数据库文件
5. 日志文件会记录详细的爬取过程，便于问题排查

## 实际运行结果

最近一次成功运行的结果：

```
=== 小程序广告爬虫 ===
爬取参数:
  时间范围: 2025-07-01 00:00:00 ~ 2025-07-30 23:59:59
  广告类型: 5
  MeetCount阈值: 10

2025-07-30 16:01:04 - INFO - 正在请求第 1 页数据...
2025-07-30 16:01:05 - INFO - 第 1 页处理完成: 50/50 条记录保存成功
2025-07-30 16:01:05 - INFO - MeetCount范围: 594501 - 594501
...
2025-07-30 16:03:02 - INFO - 第 55 页处理完成: 50/50 条记录保存成功
2025-07-30 16:03:02 - INFO - MeetCount范围: 10 - 11
2025-07-30 16:03:02 - INFO - 最小meetCount (10) <= 阈值 (10)，停止爬取
2025-07-30 16:03:02 - INFO - 爬取完成: 共 55 页，2750 条记录

爬取完成!
总页数: 55
总记录数: 2750
完成时间: 2025-07-30T16:03:02.794868
```

### 数据统计
- **总广告数**: 2744条
- **MeetCount范围**: 10 - 594,501
- **平均MeetCount**: 2663.89
- **高质量数据** (meetCount >= 100): 1310条
- **超高质量数据** (meetCount >= 500): 706条

## 快速开始

1. **初始化数据库**
   ```bash
   python3 run_crawler.py init
   ```

2. **开始爬取**
   ```bash
   python3 run_crawler.py crawl
   ```

3. **查看统计**
   ```bash
   python3 run_crawler.py stats
   ```

4. **导出数据**
   ```bash
   python3 run_crawler.py export --threshold 100
   ```
