#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小程序广告爬虫脚本
自动爬取小程序广告数据并保存到数据库
"""

import requests
import json
import time
import logging
from datetime import datetime
from database_manager import DatabaseManager

class WeappAdCrawler:
    def __init__(self, db_path="weapp_ads.db"):
        """
        初始化爬虫
        
        Args:
            db_path (str): 数据库文件路径
        """
        self.db_manager = DatabaseManager(db_path)
        self.session = requests.Session()
        self.setup_logging()
        
        # API配置
        self.base_url = "http://game.raisedsun.com/prod-api/weapp-ad-info/query-all"
        self.headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImEwNTUzOTk4LTY0NzctNGQ4Yi04ZTc1LWQ1NTMzOTc0OGFlNyJ9.BcaeJuNmbxwBf5E3DglJ-jaFg-CuUj6_nRXToHQ7DRlAcXdsBIz9JbVufaqolKmVqXN07pOhTKgfoU09Fw2J9w',
            'Proxy-Connection': 'keep-alive',
            'Referer': 'http://game.raisedsun.com/weapp/ad/index',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'
        }
        
        self.cookies = {
            'Admin-Token': 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImEwNTUzOTk4LTY0NzctNGQ4Yi04ZTc1LWQ1NTMzOTc0OGFlNyJ9.BcaeJuNmbxwBf5E3DglJ-jaFg-CuUj6_nRXToHQ7DRlAcXdsBIz9JbVufaqolKmVqXN07pOhTKgfoU09Fw2J9w'
        }
        
        # 爬取配置
        self.meet_count_threshold = 10  # meetCount阈值
        self.page_size = 50
        self.delay_between_requests = 1  # 请求间隔（秒）
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('weapp_crawler.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def build_request_params(self, page_no=1, last_appear_time_gte=1751299200000, 
                           last_appear_time_lte=1753891199999, ad_type=5):
        """
        构建请求参数
        
        Args:
            page_no (int): 页码
            last_appear_time_gte (int): 最后出现时间下限
            last_appear_time_lte (int): 最后出现时间上限
            ad_type (int): 广告类型
        
        Returns:
            dict: 请求参数
        """
        return {
            'lastAppearTimeGte': last_appear_time_gte,
            'lastAppearTimeLte': last_appear_time_lte,
            'adType': ad_type,
            'pageNo': page_no,
            'pageSize': self.page_size,
            'sort': 'meet_count desc'
        }
    
    def fetch_page_data(self, page_no, **kwargs):
        """
        获取单页数据
        
        Args:
            page_no (int): 页码
            **kwargs: 其他请求参数
        
        Returns:
            dict: API响应数据，失败返回None
        """
        params = self.build_request_params(page_no, **kwargs)
        
        try:
            self.logger.info(f"正在请求第 {page_no} 页数据...")
            
            response = self.session.get(
                self.base_url,
                params=params,
                headers=self.headers,
                cookies=self.cookies,
                verify=False,  # 对应curl的--insecure
                timeout=30
            )
            
            response.raise_for_status()
            data = response.json()
            
            if data.get('code') == 200:
                return data.get('data', {})
            else:
                self.logger.error(f"API返回错误: {data.get('msg', '未知错误')}")
                return None
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"请求第 {page_no} 页失败: {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"解析第 {page_no} 页响应JSON失败: {e}")
            return None
    
    def process_page_data(self, page_data, page_no):
        """
        处理单页数据

        Args:
            page_data (dict): 页面数据
            page_no (int): 页码

        Returns:
            tuple: (是否继续爬取, 最小meetCount, 最大meetCount, 记录数)
        """
        records = page_data.get('list', [])  # API返回的是'list'而不是'records'
        total = page_data.get('total', 0)
        
        if not records:
            self.logger.warning(f"第 {page_no} 页没有数据")
            return False, 0, 0, 0
        
        meet_counts = []
        success_count = 0
        
        for record in records:
            meet_count = record.get('meetCount', 0)
            meet_counts.append(meet_count)
            
            # 保存到数据库
            if self.db_manager.insert_ad_data(record):
                success_count += 1
        
        min_meet_count = min(meet_counts) if meet_counts else 0
        max_meet_count = max(meet_counts) if meet_counts else 0
        
        self.logger.info(f"第 {page_no} 页处理完成: {success_count}/{len(records)} 条记录保存成功")
        self.logger.info(f"MeetCount范围: {min_meet_count} - {max_meet_count}")
        
        # 记录爬取日志
        self.db_manager.log_crawl_record(
            page_no, len(records), min_meet_count, max_meet_count, 
            "success", None
        )
        
        # 判断是否继续爬取
        should_continue = min_meet_count > self.meet_count_threshold
        
        if not should_continue:
            self.logger.info(f"最小meetCount ({min_meet_count}) <= 阈值 ({self.meet_count_threshold})，停止爬取")
        
        return should_continue, min_meet_count, max_meet_count, len(records)
    
    def crawl_all_pages(self, **kwargs):
        """
        爬取所有页面数据
        
        Args:
            **kwargs: 请求参数
        
        Returns:
            dict: 爬取结果统计
        """
        self.logger.info("开始爬取小程序广告数据...")
        
        page_no = 1
        total_records = 0
        total_pages = 0
        
        while True:
            # 获取页面数据
            page_data = self.fetch_page_data(page_no, **kwargs)
            
            if page_data is None:
                self.logger.error(f"获取第 {page_no} 页数据失败，停止爬取")
                self.db_manager.log_crawl_record(
                    page_no, 0, 0, 0, "error", "获取页面数据失败"
                )
                break
            
            # 处理页面数据
            should_continue, min_meet, max_meet, record_count = self.process_page_data(page_data, page_no)
            
            total_records += record_count
            total_pages += 1
            
            # 检查是否继续爬取
            if not should_continue:
                break
            
            # 检查是否还有更多页面
            total_available = page_data.get('total', 0)
            if page_no * self.page_size >= total_available:
                self.logger.info("已爬取所有可用页面")
                break
            
            # 延迟后继续下一页
            page_no += 1
            time.sleep(self.delay_between_requests)
        
        result = {
            'total_pages': total_pages,
            'total_records': total_records,
            'completed_at': datetime.now().isoformat()
        }
        
        self.logger.info(f"爬取完成: 共 {total_pages} 页，{total_records} 条记录")
        return result
    
    def run(self, **kwargs):
        """
        运行爬虫
        
        Args:
            **kwargs: 请求参数
        """
        try:
            result = self.crawl_all_pages(**kwargs)
            
            # 显示统计信息
            stats = self.db_manager.get_statistics()
            if stats:
                self.logger.info(f"数据库统计: 总记录数 {stats['total_ads']}")
            
            return result
            
        except KeyboardInterrupt:
            self.logger.info("用户中断爬取")
        except Exception as e:
            self.logger.error(f"爬取过程中发生错误: {e}")
        finally:
            self.session.close()

if __name__ == "__main__":
    # 创建爬虫实例
    crawler = WeappAdCrawler()
    
    # 开始爬取
    # 可以自定义时间范围和广告类型
    result = crawler.run(
        last_appear_time_gte=1751299200000,
        last_appear_time_lte=1753891199999,
        ad_type=5
    )
    
    print(f"\n爬取结果: {result}")
